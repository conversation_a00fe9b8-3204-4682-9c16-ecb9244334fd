# Adobe Scan 文档边缘检测 Python 实现依赖

# 核心依赖
tensorflow>=2.8.0,<3.0.0          # TensorFlow Lite 推理引擎
opencv-python>=4.5.0              # 图像处理和计算机视觉
numpy>=1.19.0                     # 数值计算
matplotlib>=3.3.0                 # 可视化 (可选)

# 可选依赖 (用于更好的性能)
tensorflow-gpu>=2.8.0,<3.0.0      # GPU 加速 (如果有 CUDA)
pillow>=8.0.0                     # 图像处理替代方案

# 开发和测试依赖
pytest>=6.0.0                     # 单元测试
pytest-cov>=2.10.0               # 测试覆盖率
