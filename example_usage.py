#!/usr/bin/env python3
"""
Adobe Scan 文档边缘检测使用示例

展示如何使用提取的 Adobe Scan AI 模型进行文档边缘检测
"""

import cv2
import numpy as np
from document_edge_detector import AdobeScanDocumentDetector
import os


def create_test_document_image():
    """创建一个测试文档图像"""
    # 创建一个白色背景
    image = np.ones((800, 600, 3), dtype=np.uint8) * 128
    
    # 绘制一个文档形状 (梯形，模拟透视效果)
    document_corners = np.array([
        [100, 150],   # 左上
        [500, 120],   # 右上  
        [520, 650],   # 右下
        [80, 680]     # 左下
    ], dtype=np.int32)
    
    # 填充文档区域为白色
    cv2.fillPoly(image, [document_corners], (255, 255, 255))
    
    # 添加一些文本内容
    cv2.putText(image, "Sample Document", (150, 300), 
                cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 0, 0), 3)
    cv2.putText(image, "This is a test document", (120, 400), 
                cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    cv2.putText(image, "for edge detection", (150, 500), 
                cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    
    # 添加一些噪声和阴影效果
    noise = np.random.normal(0, 10, image.shape).astype(np.int16)
    image = np.clip(image.astype(np.int16) + noise, 0, 255).astype(np.uint8)
    
    return image


def demo_basic_usage():
    """基本使用示例"""
    print("=== Adobe Scan 文档边缘检测基本使用示例 ===\n")
    
    # 1. 初始化检测器
    print("1. 初始化 Adobe Scan 检测器...")
    try:
        detector = AdobeScanDocumentDetector()
        print("✅ 检测器初始化成功\n")
    except Exception as e:
        print(f"❌ 检测器初始化失败: {e}")
        return
    
    # 2. 创建测试图像
    print("2. 创建测试文档图像...")
    test_image = create_test_document_image()
    cv2.imwrite("test_document.jpg", test_image)
    print("✅ 测试图像已保存为 test_document.jpg\n")
    
    # 3. 检测文档边缘
    print("3. 运行 Adobe Scan AI 模型检测...")
    corners = detector.detect_document_edges(test_image)
    
    if corners is not None:
        print("✅ 检测成功!")
        print(f"检测到 {len(corners)} 个角点:")
        
        # 显示角点坐标
        corner_names = ["左上", "右上", "右下", "左下"]
        for i, (x, y) in enumerate(corners):
            name = corner_names[i] if i < len(corner_names) else f"角点{i+1}"
            print(f"  {name}: ({x:.4f}, {y:.4f})")
        
        # 4. 可视化结果
        print("\n4. 生成可视化结果...")
        result_image = detector.visualize_detection(test_image, corners, "detection_result.jpg")
        print("✅ 检测结果已保存为 detection_result.jpg")
        
        # 5. 计算文档区域面积 (归一化)
        if len(corners) == 4:
            area = cv2.contourArea(corners.reshape(-1, 1, 2))
            print(f"\n📏 检测到的文档区域面积: {area:.4f} (归一化)")
        
    else:
        print("❌ 检测失败: 未能检测到文档边缘")


def demo_batch_processing():
    """批量处理示例"""
    print("\n=== 批量处理示例 ===\n")
    
    # 创建多个测试图像
    test_images = []
    for i in range(3):
        # 创建不同角度的文档
        image = create_test_document_image()
        
        # 添加旋转
        if i == 1:
            center = (image.shape[1]//2, image.shape[0]//2)
            rotation_matrix = cv2.getRotationMatrix2D(center, 15, 1.0)
            image = cv2.warpAffine(image, rotation_matrix, (image.shape[1], image.shape[0]))
        elif i == 2:
            center = (image.shape[1]//2, image.shape[0]//2)
            rotation_matrix = cv2.getRotationMatrix2D(center, -10, 1.0)
            image = cv2.warpAffine(image, rotation_matrix, (image.shape[1], image.shape[0]))
        
        test_images.append(image)
        cv2.imwrite(f"test_document_{i+1}.jpg", image)
    
    # 初始化检测器
    detector = AdobeScanDocumentDetector()
    
    # 批量处理
    results = []
    for i, image in enumerate(test_images):
        print(f"处理图像 {i+1}/3...")
        corners = detector.detect_document_edges(image)
        
        if corners is not None:
            result_image = detector.visualize_detection(image, corners, f"result_{i+1}.jpg")
            results.append(corners)
            print(f"✅ 图像 {i+1} 检测成功")
        else:
            results.append(None)
            print(f"❌ 图像 {i+1} 检测失败")
    
    # 统计结果
    success_count = sum(1 for r in results if r is not None)
    print(f"\n📊 批量处理结果: {success_count}/{len(test_images)} 成功")


def demo_real_image():
    """真实图像处理示例"""
    print("\n=== 真实图像处理示例 ===\n")
    
    # 检查是否有真实图像文件
    real_image_paths = [
        "document.jpg", "document.png", "test.jpg", "test.png",
        "sample.jpg", "sample.png"
    ]
    
    real_image_path = None
    for path in real_image_paths:
        if os.path.exists(path):
            real_image_path = path
            break
    
    if real_image_path:
        print(f"发现真实图像: {real_image_path}")
        
        # 加载图像
        image = cv2.imread(real_image_path)
        if image is not None:
            print(f"图像尺寸: {image.shape}")
            
            # 初始化检测器
            detector = AdobeScanDocumentDetector()
            
            # 检测
            corners = detector.detect_document_edges(image)
            
            if corners is not None:
                # 保存结果
                result_path = f"real_result_{os.path.basename(real_image_path)}"
                detector.visualize_detection(image, corners, result_path)
                print(f"✅ 真实图像检测成功，结果保存为: {result_path}")
            else:
                print("❌ 真实图像检测失败")
        else:
            print(f"❌ 无法加载图像: {real_image_path}")
    else:
        print("ℹ️  未找到真实图像文件")
        print("提示: 将文档图像命名为 document.jpg 或 test.jpg 放在当前目录")


def main():
    """主函数"""
    print("🚀 Adobe Scan 文档边缘检测 Python 实现演示\n")
    print("基于逆向工程提取的 TensorFlow Lite 模型")
    print("模型: capture_6_22_23.tflite (2.42 MB)")
    print("=" * 60)
    
    try:
        # 基本使用示例
        demo_basic_usage()
        
        # 批量处理示例
        demo_batch_processing()
        
        # 真实图像处理示例
        demo_real_image()
        
        print("\n" + "=" * 60)
        print("🎉 演示完成!")
        print("\n生成的文件:")
        print("  - test_document.jpg: 测试文档图像")
        print("  - detection_result.jpg: 检测结果可视化")
        print("  - test_document_*.jpg: 批量测试图像")
        print("  - result_*.jpg: 批量检测结果")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")


if __name__ == "__main__":
    main()
