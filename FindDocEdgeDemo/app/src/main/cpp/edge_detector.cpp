#include <jni.h>
#include <string>
#include <vector>
#include <android/log.h>
#include <android/asset_manager.h>
#include <android/asset_manager_jni.h>

#define LOG_TAG "EdgeDetectorNative"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

// 检测文档边缘的JNI方法
extern "C" JNIEXPORT jfloatArray JNICALL
Java_com_example_finddocedgedemo_DocumentEdgeDetector_detectEdgesNative(
        JNIEnv* env,
        jobject /* this */,
        jbyteArray inputArray,
        jint width,
        jint height,
        jobject assetManager,
        jstring modelPath) {
    
    LOGI("开始本地文档边缘检测");
    
    // 获取输入数据
    jbyte* input = env->GetByteArrayElements(inputArray, nullptr);
    jsize inputLength = env->GetArrayLength(inputArray);
    
    // 获取模型路径
    const char* modelPathCStr = env->GetStringUTFChars(modelPath, nullptr);
    
    // 这里只是一个模拟实现，返回4个角点的固定坐标
    // 实际应用中需要使用TensorFlow Lite进行边缘检测
    
    // 创建返回值数组（4个角点，每个角点2个坐标 x,y）
    jfloatArray result = env->NewFloatArray(8);
    
    // 模拟4个角点的坐标（归一化到0-1范围）
    float corners[8] = {
        0.1f, 0.1f,  // 左上
        0.9f, 0.1f,  // 右上
        0.9f, 0.9f,  // 右下
        0.1f, 0.9f   // 左下
    };
    
    // 设置返回值
    env->SetFloatArrayRegion(result, 0, 8, corners);
    
    // 释放资源
    env->ReleaseByteArrayElements(inputArray, input, JNI_ABORT);
    env->ReleaseStringUTFChars(modelPath, modelPathCStr);
    
    LOGI("本地文档边缘检测完成");
    
    return result;
}

// 初始化模型的JNI方法
extern "C" JNIEXPORT jboolean JNICALL
Java_com_example_finddocedgedemo_DocumentEdgeDetector_initModelNative(
        JNIEnv* env,
        jobject /* this */,
        jobject assetManager,
        jstring modelPath) {
    
    // 获取模型路径
    const char* modelPathCStr = env->GetStringUTFChars(modelPath, nullptr);
    
    LOGI("初始化模型: %s", modelPathCStr);
    
    // 实际应用中需要加载TensorFlow Lite模型
    
    // 释放资源
    env->ReleaseStringUTFChars(modelPath, modelPathCStr);
    
    return JNI_TRUE;
}

// 释放模型资源的JNI方法
extern "C" JNIEXPORT void JNICALL
Java_com_example_finddocedgedemo_DocumentEdgeDetector_releaseModelNative(
        JNIEnv* env,
        jobject /* this */) {
    
    LOGI("释放模型资源");
    
    // 实际应用中需要释放TensorFlow Lite模型
} 