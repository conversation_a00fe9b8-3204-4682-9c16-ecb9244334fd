package com.example.finddocedgedemo

import android.Manifest
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.PointF
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.example.finddocedgedemo.databinding.ActivityMainBinding
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 主活动 - Adobe Scan 文档边缘检测演示
 * 集成 CameraX + TensorFlow Lite + Adobe Scan 模型
 */
class MainActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "MainActivity"
        private val REQUIRED_PERMISSIONS = arrayOf(Manifest.permission.CAMERA)
        
        // Used to load the 'finddocedgedemo' library on application startup.
        init {
            System.loadLibrary("finddocedgedemo")
        }
    }

    private lateinit var binding: ActivityMainBinding
    private lateinit var cameraManager: CameraManager
    private lateinit var documentDetector: DocumentEdgeDetector

    // 检测状态
    private var isDetecting = false
    private var detectionCount = 0

    // 权限请求
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        if (allPermissionsGranted()) {
            startCamera()
        } else {
            Toast.makeText(this, "需要相机权限才能使用文档扫描功能", Toast.LENGTH_LONG).show()
            finish()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 初始化组件
        initializeComponents()

        // 设置UI事件
        setupUI()

        // 检查权限
        if (allPermissionsGranted()) {
            startCamera()
        } else {
            requestPermissions()
        }
    }

    /**
     * 初始化组件
     */
    private fun initializeComponents() {
        Log.d(TAG, "初始化 Adobe Scan 文档边缘检测系统...")

        // 初始化文档检测器
        documentDetector = DocumentEdgeDetector(this)

        // 初始化相机管理器
        cameraManager = CameraManager(this, this, binding.previewView)

        // 设置图像分析监听器
        cameraManager.setOnImageAnalyzedListener(object : CameraManager.OnImageAnalyzedListener {
            override fun onImageAnalyzed(bitmap: Bitmap) {
                processImage(bitmap)
            }

            override fun onAnalysisError(error: String) {
                runOnUiThread {
                    binding.scannerView.setDetectionStatus("分析错误: $error")
                    Log.e(TAG, "图像分析错误: $error")
                }
            }
        })

        // 异步初始化模型
        lifecycleScope.launch {
            initializeModel()
        }
    }

    /**
     * 设置UI事件
     */
    private fun setupUI() {
        // 拍照按钮
        binding.captureButton.setOnClickListener {
            captureDocument()
        }

        // 闪光灯按钮
        binding.flashButton.setOnClickListener {
            toggleFlash()
        }

        // 信息按钮
        binding.infoButton.setOnClickListener {
            showCameraInfo()
        }

        // 设置初始状态
        binding.scannerView.setDetectionStatus("正在初始化模型...")
    }

    /**
     * 异步初始化模型
     */
    private suspend fun initializeModel() {
        withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "开始加载 Adobe Scan 模型...")
                val success = documentDetector.initialize()

                withContext(Dispatchers.Main) {
                    if (success) {
                        binding.scannerView.setDetectionStatus("模型加载成功，开始检测...")
                        Toast.makeText(this@MainActivity, "Adobe Scan 模型加载成功", Toast.LENGTH_SHORT).show()
                        Log.d(TAG, "Adobe Scan 模型初始化成功")
                    } else {
                        binding.scannerView.setDetectionStatus("模型加载失败")
                        Toast.makeText(this@MainActivity, "模型加载失败", Toast.LENGTH_LONG).show()
                        Log.e(TAG, "Adobe Scan 模型初始化失败")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "模型初始化异常", e)
                withContext(Dispatchers.Main) {
                    binding.scannerView.setDetectionStatus("模型初始化异常")
                    Toast.makeText(this@MainActivity, "模型初始化异常: ${e.message}", Toast.LENGTH_LONG).show()
                }
            }
        }
    }

    /**
     * 处理图像
     */
    private fun processImage(bitmap: Bitmap) {
        if (!documentDetector.isReady() || isDetecting) {
            return
        }

        isDetecting = true
        detectionCount++

        lifecycleScope.launch(Dispatchers.IO) {
            try {
                // 使用 Adobe Scan 模型检测文档边缘
                val corners = documentDetector.detectEdges(bitmap)

                withContext(Dispatchers.Main) {
                    // 更新UI显示检测结果
                    binding.scannerView.updateDetectedCorners(corners)

                    // 更新统计信息
                    val statusText = if (corners != null && corners.size == 4) {
                        "检测成功 #$detectionCount - 发现文档边缘"
                    } else {
                        "检测中 #$detectionCount - 寻找文档..."
                    }
                    binding.scannerView.setDetectionStatus(statusText)

                    // 日志输出
                    if (corners != null) {
                        Log.d(TAG, "检测结果 #$detectionCount: ${corners.size} 个角点")
                        corners.forEachIndexed { index, point ->
                            Log.d(TAG, "  角点[$index]: (${point.x}, ${point.y})")
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "文档检测异常", e)
                withContext(Dispatchers.Main) {
                    binding.scannerView.setDetectionStatus("检测异常: ${e.message}")
                }
            } finally {
                isDetecting = false
            }
        }
    }

    /**
     * 拍摄文档
     */
    private fun captureDocument() {
        if (!binding.scannerView.isDocumentReady()) {
            Toast.makeText(this, "请先检测到文档边缘", Toast.LENGTH_SHORT).show()
            return
        }

        val corners = binding.scannerView.getDetectedCorners()
        if (corners != null && corners.size == 4) {
            // 这里可以实现文档拍摄和透视变换
            Toast.makeText(this, "文档拍摄功能待实现", Toast.LENGTH_SHORT).show()
            Log.d(TAG, "准备拍摄文档，角点: ${corners.contentToString()}")
        }
    }

    /**
     * 切换闪光灯
     */
    private fun toggleFlash() {
        val isOn = cameraManager.toggleFlash()
        val status = if (isOn) "开启" else "关闭"
        Toast.makeText(this, "闪光灯已$status", Toast.LENGTH_SHORT).show()
    }

    /**
     * 显示相机信息
     */
    private fun showCameraInfo() {
        val info = cameraManager.getCameraInfo()
        Toast.makeText(this, info, Toast.LENGTH_LONG).show()
        Log.d(TAG, info)
    }

    /**
     * 启动相机
     */
    private fun startCamera() {
        Log.d(TAG, "启动相机...")
        cameraManager.startCamera()
    }

    /**
     * 检查权限
     */
    private fun allPermissionsGranted() = REQUIRED_PERMISSIONS.all {
        ContextCompat.checkSelfPermission(baseContext, it) == PackageManager.PERMISSION_GRANTED
    }

    /**
     * 请求权限
     */
    private fun requestPermissions() {
        requestPermissionLauncher.launch(REQUIRED_PERMISSIONS)
    }

    override fun onDestroy() {
        super.onDestroy()

        // 释放资源
        cameraManager.release()
        documentDetector.release()

        Log.d(TAG, "资源已释放")
    }

    /**
     * A native method that is implemented by the 'finddocedgedemo' native library,
     * which is packaged with this application.
     */
    external fun stringFromJNI(): String
}