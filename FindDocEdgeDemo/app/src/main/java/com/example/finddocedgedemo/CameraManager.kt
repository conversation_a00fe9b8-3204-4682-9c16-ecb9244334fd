package com.example.finddocedgedemo

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.ImageFormat
import android.graphics.Matrix
import android.graphics.Rect
import android.graphics.YuvImage
import android.util.Log
import android.util.Size
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import java.io.ByteArrayOutputStream
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

/**
 * 相机管理器
 * 负责 CameraX 的初始化、预览和图像分析
 */
class CameraManager(
    private val context: Context,
    private val lifecycleOwner: LifecycleOwner,
    private val previewView: PreviewView
) {
    
    companion object {
        private const val TAG = "CameraManager"
        private const val ANALYSIS_TARGET_WIDTH = 640
        private const val ANALYSIS_TARGET_HEIGHT = 480
    }
    
    // 相机相关
    private var cameraProvider: ProcessCameraProvider? = null
    private var camera: Camera? = null
    private var preview: Preview? = null
    private var imageAnalyzer: ImageAnalysis? = null
    
    // 线程池
    private val cameraExecutor: ExecutorService = Executors.newSingleThreadExecutor()
    
    // 回调接口
    interface OnImageAnalyzedListener {
        fun onImageAnalyzed(bitmap: Bitmap)
        fun onAnalysisError(error: String)
    }
    
    private var imageAnalyzedListener: OnImageAnalyzedListener? = null
    
    /**
     * 设置图像分析监听器
     */
    fun setOnImageAnalyzedListener(listener: OnImageAnalyzedListener) {
        imageAnalyzedListener = listener
    }
    
    /**
     * 启动相机
     */
    fun startCamera() {
        val cameraProviderFuture = ProcessCameraProvider.getInstance(context)
        
        cameraProviderFuture.addListener({
            try {
                cameraProvider = cameraProviderFuture.get()
                bindCameraUseCases()
                Log.d(TAG, "相机启动成功")
            } catch (e: Exception) {
                Log.e(TAG, "相机启动失败", e)
                imageAnalyzedListener?.onAnalysisError("相机启动失败: ${e.message}")
            }
        }, ContextCompat.getMainExecutor(context))
    }
    
    /**
     * 绑定相机用例
     */
    private fun bindCameraUseCases() {
        val cameraProvider = cameraProvider ?: return
        
        // 预览用例
        preview = Preview.Builder()
            .setTargetAspectRatio(AspectRatio.RATIO_4_3)
            .build()
            .also {
                it.setSurfaceProvider(previewView.surfaceProvider)
            }
        
        // 图像分析用例
        imageAnalyzer = ImageAnalysis.Builder()
            .setTargetResolution(Size(ANALYSIS_TARGET_WIDTH, ANALYSIS_TARGET_HEIGHT))
            .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
            .build()
            .also {
                it.setAnalyzer(cameraExecutor, ImageAnalyzer())
            }
        
        // 相机选择器（后置摄像头）
        val cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA
        
        try {
            // 解绑所有用例
            cameraProvider.unbindAll()
            
            // 绑定用例到生命周期
            camera = cameraProvider.bindToLifecycle(
                lifecycleOwner,
                cameraSelector,
                preview,
                imageAnalyzer
            )
            
            Log.d(TAG, "相机用例绑定成功")
            
        } catch (e: Exception) {
            Log.e(TAG, "相机用例绑定失败", e)
            imageAnalyzedListener?.onAnalysisError("相机绑定失败: ${e.message}")
        }
    }
    
    /**
     * 图像分析器
     */
    private inner class ImageAnalyzer : ImageAnalysis.Analyzer {
        
        override fun analyze(image: ImageProxy) {
            try {
                // 转换 ImageProxy 到 Bitmap
                val bitmap = imageProxyToBitmap(image)
                
                if (bitmap != null) {
                    // 回调分析结果
                    imageAnalyzedListener?.onImageAnalyzed(bitmap)
                } else {
                    imageAnalyzedListener?.onAnalysisError("图像转换失败")
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "图像分析失败", e)
                imageAnalyzedListener?.onAnalysisError("图像分析失败: ${e.message}")
            } finally {
                image.close()
            }
        }
    }
    
    /**
     * 将 ImageProxy 转换为 Bitmap
     */
    private fun imageProxyToBitmap(image: ImageProxy): Bitmap? {
        return try {
            when (image.format) {
                ImageFormat.YUV_420_888 -> {
                    yuvToBitmap(image)
                }
                ImageFormat.JPEG -> {
                    val buffer = image.planes[0].buffer
                    val bytes = ByteArray(buffer.remaining())
                    buffer.get(bytes)
                    BitmapFactory.decodeByteArray(bytes, 0, bytes.size)
                }
                else -> {
                    Log.w(TAG, "不支持的图像格式: ${image.format}")
                    null
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "图像转换失败", e)
            null
        }
    }
    
    /**
     * 将 YUV_420_888 格式转换为 Bitmap
     */
    private fun yuvToBitmap(image: ImageProxy): Bitmap? {
        return try {
            val yBuffer = image.planes[0].buffer
            val uBuffer = image.planes[1].buffer
            val vBuffer = image.planes[2].buffer
            
            val ySize = yBuffer.remaining()
            val uSize = uBuffer.remaining()
            val vSize = vBuffer.remaining()
            
            val nv21 = ByteArray(ySize + uSize + vSize)
            
            // Y 平面
            yBuffer.get(nv21, 0, ySize)
            
            // UV 平面交错排列
            val uvPixelStride = image.planes[1].pixelStride
            if (uvPixelStride == 1) {
                uBuffer.get(nv21, ySize, uSize)
                vBuffer.get(nv21, ySize + uSize, vSize)
            } else {
                // 处理像素步长不为1的情况
                val uvBuffer = ByteArray(uSize + vSize)
                uBuffer.get(uvBuffer, 0, uSize)
                vBuffer.get(uvBuffer, uSize, vSize)
                
                var uvIndex = 0
                for (i in 0 until uSize step uvPixelStride) {
                    nv21[ySize + uvIndex] = uvBuffer[i]
                    nv21[ySize + uvIndex + 1] = uvBuffer[uSize + i]
                    uvIndex += 2
                }
            }
            
            // 转换为 Bitmap
            val yuvImage = YuvImage(nv21, ImageFormat.NV21, image.width, image.height, null)
            val out = ByteArrayOutputStream()
            yuvImage.compressToJpeg(Rect(0, 0, image.width, image.height), 100, out)
            val imageBytes = out.toByteArray()
            val bitmap = BitmapFactory.decodeByteArray(imageBytes, 0, imageBytes.size)
            
            // 根据图像旋转角度调整 Bitmap
            rotateImageIfRequired(bitmap, image.imageInfo.rotationDegrees)
            
        } catch (e: Exception) {
            Log.e(TAG, "YUV 转 Bitmap 失败", e)
            null
        }
    }
    
    /**
     * 根据需要旋转图像
     */
    private fun rotateImageIfRequired(bitmap: Bitmap, rotationDegrees: Int): Bitmap {
        return if (rotationDegrees != 0) {
            val matrix = Matrix()
            matrix.postRotate(rotationDegrees.toFloat())
            Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true)
        } else {
            bitmap
        }
    }
    
    /**
     * 拍照
     */
    fun takePicture(onImageCaptured: (Bitmap?) -> Unit) {
        // 这里可以实现拍照功能
        // 暂时使用当前预览帧
        Log.d(TAG, "拍照功能待实现")
    }
    
    /**
     * 切换闪光灯
     */
    fun toggleFlash(): Boolean {
        return try {
            val camera = camera ?: return false
            val currentFlashMode = camera.cameraInfo.torchState.value
            val newFlashMode = currentFlashMode != TorchState.ON
            camera.cameraControl.enableTorch(newFlashMode)
            Log.d(TAG, "闪光灯状态: ${if (newFlashMode) "开启" else "关闭"}")
            newFlashMode
        } catch (e: Exception) {
            Log.e(TAG, "切换闪光灯失败", e)
            false
        }
    }
    
    /**
     * 获取相机信息
     */
    fun getCameraInfo(): String {
        val camera = camera ?: return "相机未初始化"
        val cameraInfo = camera.cameraInfo
        
        return buildString {
            appendLine("相机信息:")
            appendLine("- 传感器旋转角度: ${cameraInfo.sensorRotationDegrees}")
            appendLine("- 闪光灯可用: ${cameraInfo.hasFlashUnit()}")
            appendLine("- 当前闪光灯状态: ${cameraInfo.torchState.value}")
            
            // 移除有问题的代码，直接返回基本信息
            appendLine("- 分辨率: ${camera.cameraInfo.sensorRotationDegrees}°")
        }
    }
    
    /**
     * 停止相机
     */
    fun stopCamera() {
        try {
            cameraProvider?.unbindAll()
            Log.d(TAG, "相机已停止")
        } catch (e: Exception) {
            Log.e(TAG, "停止相机失败", e)
        }
    }
    
    /**
     * 释放资源
     */
    fun release() {
        stopCamera()
        cameraExecutor.shutdown()
        Log.d(TAG, "相机资源已释放")
    }
}
