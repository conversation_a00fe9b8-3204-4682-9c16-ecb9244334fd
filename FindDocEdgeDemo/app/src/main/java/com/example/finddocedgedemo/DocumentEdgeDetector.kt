package com.example.finddocedgedemo

import android.content.Context
import android.content.res.AssetManager
import android.graphics.Bitmap
import android.graphics.PointF
import android.util.Log
import org.tensorflow.lite.Interpreter
import java.io.FileInputStream
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.nio.MappedByteBuffer
import java.nio.channels.FileChannel

/**
 * Adobe Scan 文档边缘检测器
 * 基于逆向工程分析的 Adobe Scan 模型实现实时文档边缘检测
 */
class DocumentEdgeDetector(private val context: Context) {
    
    companion object {
        private const val TAG = "DocumentEdgeDetector"
        
        // 模型文件名（基于 Adobe Scan 分析结果）
        private const val CAPTURE_MODEL = "capture_6_22_23.tflite"  // 主要边缘检测模型 (2.42MB)
        private const val RESIZE_MODEL = "resize_to_320.tflite"     // 图像预处理模型 (7.8KB)
        private const val PAGE_TURN_MODEL = "page_turn_2im_002.tflite" // 页面方向检测模型 (1.16MB)
        
        // 输入图像尺寸（基于 Adobe Scan 分析）
        private const val INPUT_WIDTH = 640
        private const val INPUT_HEIGHT = 480
        private const val INPUT_CHANNELS = 1  // 灰度图像
        
        // 输出角点数量
        private const val OUTPUT_POINTS = 13  // 根据实际模型输出
        private const val CORNER_COORDINATES = 2  // x, y
        
        // 文档角点索引 (需要从13个点中选择4个角点)
        private val CORNER_INDICES = intArrayOf(0, 3, 9, 6)  // 左上、右上、右下、左下
        
        // 加载本地库
        init {
            System.loadLibrary("finddocedgedemo")
        }
    }
    
    // TensorFlow Lite 解释器
    private var captureInterpreter: Interpreter? = null
    private var resizeInterpreter: Interpreter? = null
    private var pageTurnInterpreter: Interpreter? = null
    
    // 输入输出缓冲区
    private var inputBuffer: ByteBuffer? = null
    private var outputBuffer: ByteBuffer? = null
    
    // 检测状态
    private var isInitialized = false
    private var useNativeDetection = false  // 禁用本地检测，改为使用Java实现
    
    /**
     * 初始化模型
     */
    fun initialize(): Boolean {
        return try {
            Log.d(TAG, "开始初始化 Adobe Scan 文档边缘检测模型...")
            
            if (useNativeDetection) {
                // 使用本地方法初始化模型
                val modelPath = "models/$CAPTURE_MODEL"
                isInitialized = initModelNative(context.assets, modelPath)
                Log.d(TAG, "本地模型初始化${if (isInitialized) "成功" else "失败"}")
            } else {
                // 使用Java初始化模型
                // 加载主要的边缘检测模型
                val captureModel = loadModelFile(CAPTURE_MODEL)
                captureInterpreter = Interpreter(captureModel, getInterpreterOptions())
                
                // 加载图像预处理模型
                val resizeModel = loadModelFile(RESIZE_MODEL)
                resizeInterpreter = Interpreter(resizeModel, getInterpreterOptions())
                
                // 加载页面方向检测模型
                val pageTurnModel = loadModelFile(PAGE_TURN_MODEL)
                pageTurnInterpreter = Interpreter(pageTurnModel, getInterpreterOptions())
                
                // 初始化输入输出缓冲区
                initializeBuffers()
                
                isInitialized = true
                Log.d(TAG, "Adobe Scan 模型初始化成功")
                
                // 打印模型信息
                logModelInfo()
            }
            
            isInitialized
        } catch (e: Exception) {
            Log.e(TAG, "模型初始化失败", e)
            false
        }
    }
    
    /**
     * 检测文档边缘
     * @param bitmap 输入图像
     * @return 检测到的四个角点坐标（归一化坐标 0-1）
     */
    fun detectEdges(bitmap: Bitmap): Array<PointF>? {
        if (!isInitialized) {
            Log.w(TAG, "模型未初始化")
            return null
        }
        
        return try {
            Log.d(TAG, "开始文档边缘检测...")
            
            if (useNativeDetection) {
                // 使用本地方法检测边缘
                // 1. 预处理图像
                val preprocessedBitmap = preprocessImage(bitmap)
                
                // 2. 转换为模型输入格式 (UINT8类型)
                val inputArray = bitmapToInputArray(preprocessedBitmap)
                
                // 3. 调用本地方法进行边缘检测
                val modelPath = "models/$CAPTURE_MODEL"
                val cornersArray = detectEdgesNative(
                    inputArray, 
                    INPUT_WIDTH, 
                    INPUT_HEIGHT, 
                    context.assets, 
                    modelPath
                )
                
                // 4. 转换结果为PointF数组
                val corners = Array(4) { PointF() }
                for (i in 0 until 4) {
                    corners[i].x = cornersArray[i * 2]
                    corners[i].y = cornersArray[i * 2 + 1]
                    Log.d(TAG, "角点[$i]: (${corners[i].x}, ${corners[i].y})")
                }
                
                corners
            } else {
                // 使用Java检测边缘
                // 1. 预处理图像
                val preprocessedBitmap = preprocessImage(bitmap)
                
                // 2. 转换为模型输入格式 (UINT8类型)
                val inputArray = bitmapToInputArray(preprocessedBitmap)
                
                // 3. 运行边缘检测模型
                val corners = runEdgeDetection(inputArray)
                
                // 4. 后处理结果
                val processedCorners = postprocessCorners(corners)
                
                Log.d(TAG, "边缘检测完成，检测到 ${processedCorners?.size ?: 0} 个角点")
                
                processedCorners
            }
        } catch (e: Exception) {
            Log.e(TAG, "边缘检测失败", e)
            null
        }
    }
    
    // Native方法声明
    private external fun initModelNative(assetManager: AssetManager, modelPath: String): Boolean
    private external fun detectEdgesNative(
        inputArray: ByteArray, 
        width: Int, 
        height: Int, 
        assetManager: AssetManager, 
        modelPath: String
    ): FloatArray
    private external fun releaseModelNative()
    
    /**
     * 预处理图像
     */
    private fun preprocessImage(bitmap: Bitmap): Bitmap {
        // 调整图像大小到模型输入尺寸 (640x480)
        val resizedBitmap = Bitmap.createScaledBitmap(bitmap, INPUT_WIDTH, INPUT_HEIGHT, true)
        
        // 转换为灰度图像
        val grayBitmap = Bitmap.createBitmap(INPUT_WIDTH, INPUT_HEIGHT, Bitmap.Config.ARGB_8888)
        val canvas = android.graphics.Canvas(grayBitmap)
        val paint = android.graphics.Paint()
        val colorMatrix = android.graphics.ColorMatrix()
        colorMatrix.setSaturation(0f)  // 转为灰度
        paint.colorFilter = android.graphics.ColorMatrixColorFilter(colorMatrix)
        canvas.drawBitmap(resizedBitmap, 0f, 0f, paint)
        
        return grayBitmap
    }
    
    /**
     * 将 Bitmap 转换为模型输入数组
     */
    private fun bitmapToInputArray(bitmap: Bitmap): ByteArray {
        val inputArray = ByteArray(INPUT_WIDTH * INPUT_HEIGHT * INPUT_CHANNELS)
        val pixels = IntArray(INPUT_WIDTH * INPUT_HEIGHT)
        
        bitmap.getPixels(pixels, 0, INPUT_WIDTH, 0, 0, INPUT_WIDTH, INPUT_HEIGHT)
        
        for (i in pixels.indices) {
            // 提取灰度值 (0-255)
            val pixel = pixels[i]
            val gray = (pixel shr 16 and 0xFF).toByte() // 取红色通道作为灰度值
            inputArray[i] = gray
        }
        
        return inputArray
    }
    
    /**
     * 运行边缘检测模型
     */
    private fun runEdgeDetection(inputArray: ByteArray): Array<FloatArray>? {
        val interpreter = captureInterpreter ?: return null
        
        // 准备输入 - 使用UINT8类型
        val input = Array(1) { Array(INPUT_HEIGHT) { Array(INPUT_WIDTH) { ByteArray(INPUT_CHANNELS) } } }
        var index = 0
        for (y in 0 until INPUT_HEIGHT) {
            for (x in 0 until INPUT_WIDTH) {
                input[0][y][x][0] = inputArray[index++]
            }
        }
        
        // 准备输出 - 使用与模型输出匹配的形状 [1, 13, 2]
        val output = Array(1) { Array(OUTPUT_POINTS) { FloatArray(CORNER_COORDINATES) } }
        
        try {
            // 运行推理
            interpreter.run(input, output)
            Log.d(TAG, "模型推理成功，输出形状: [1, ${OUTPUT_POINTS}, ${CORNER_COORDINATES}]")
            
            // 打印所有检测点的坐标
            for (i in 0 until OUTPUT_POINTS) {
                Log.d(TAG, "检测点[$i]: (${output[0][i][0]}, ${output[0][i][1]})")
            }
            
            return output[0]
        } catch (e: Exception) {
            Log.e(TAG, "模型推理失败", e)
            return null
        }
    }
    
    /**
     * 后处理角点坐标
     */
    private fun postprocessCorners(corners: Array<FloatArray>?): Array<PointF>? {
        if (corners == null) {
            return null
        }
        
        try {
            // 从13个检测点中选择4个角点
            val points = Array(4) { PointF() }
            
            for (i in 0 until 4) {
                val index = CORNER_INDICES[i]
                if (index < corners.size && corners[index].size >= CORNER_COORDINATES) {
                    // Adobe Scan 输出归一化坐标 (0-1 范围)
                    points[i].x = corners[index][0].coerceIn(0f, 1f)
                    points[i].y = corners[index][1].coerceIn(0f, 1f)
                    
                    Log.d(TAG, "角点[$i] (来自检测点[$index]): (${points[i].x}, ${points[i].y})")
                } else {
                    // 如果角点索引无效，设置默认值
                    Log.w(TAG, "角点索引 $index 超出范围，使用默认值")
                    points[i] = PointF(0.5f, 0.5f)
                }
            }
            
            return points
        } catch (e: Exception) {
            Log.e(TAG, "处理角点失败", e)
            return null
        }
    }
    
    /**
     * 加载模型文件
     */
    private fun loadModelFile(modelName: String): MappedByteBuffer {
        val assetFileDescriptor = context.assets.openFd("models/$modelName")
        val fileInputStream = FileInputStream(assetFileDescriptor.fileDescriptor)
        val fileChannel = fileInputStream.channel
        val startOffset = assetFileDescriptor.startOffset
        val declaredLength = assetFileDescriptor.declaredLength
        
        Log.d(TAG, "加载模型: $modelName, 大小: ${declaredLength / 1024 / 1024}MB")
        
        return fileChannel.map(FileChannel.MapMode.READ_ONLY, startOffset, declaredLength)
    }
    
    /**
     * 获取解释器选项
     */
    private fun getInterpreterOptions(): Interpreter.Options {
        val options = Interpreter.Options()
        
        // 设置线程数
        options.setNumThreads(4)
        
        // 禁用GPU加速，只使用CPU
        // 原代码中尝试使用GPU代理但找不到相关类，所以直接使用CPU模式
        Log.d(TAG, "使用CPU模式运行TensorFlow Lite模型")
        
        return options
    }
    
    /**
     * 初始化缓冲区
     */
    private fun initializeBuffers() {
        // 输入缓冲区
        val inputSize = INPUT_WIDTH * INPUT_HEIGHT * INPUT_CHANNELS * 4 // Float32
        inputBuffer = ByteBuffer.allocateDirect(inputSize)
        inputBuffer?.order(ByteOrder.nativeOrder())
        
        // 输出缓冲区
        val outputSize = OUTPUT_POINTS * CORNER_COORDINATES * 4 // Float32
        outputBuffer = ByteBuffer.allocateDirect(outputSize)
        outputBuffer?.order(ByteOrder.nativeOrder())
    }
    
    /**
     * 打印模型信息
     */
    private fun logModelInfo() {
        captureInterpreter?.let { interpreter ->
            try {
                Log.d(TAG, "=== Adobe Scan 边缘检测模型信息 ===")
                
                // 打印所有输入张量信息
                Log.d(TAG, "模型输入张量数量: ${interpreter.inputTensorCount}")
                for (i in 0 until interpreter.inputTensorCount) {
                    val inputTensor = interpreter.getInputTensor(i)
                    Log.d(TAG, "输入张量[$i] 形状: ${inputTensor.shape().contentToString()}")
                    Log.d(TAG, "输入张量[$i] 数据类型: ${inputTensor.dataType()}")
                    try {
                        Log.d(TAG, "输入张量[$i] 量化参数: 缩放比例=${inputTensor.quantizationParams().scale}, 零点=${inputTensor.quantizationParams().zeroPoint}")
                    } catch (e: Exception) {
                        Log.d(TAG, "输入张量[$i] 无量化参数")
                    }
                }
                
                // 打印所有输出张量信息
                Log.d(TAG, "模型输出张量数量: ${interpreter.outputTensorCount}")
                for (i in 0 until interpreter.outputTensorCount) {
                    val outputTensor = interpreter.getOutputTensor(i)
                    Log.d(TAG, "输出张量[$i] 形状: ${outputTensor.shape().contentToString()}")
                    Log.d(TAG, "输出张量[$i] 数据类型: ${outputTensor.dataType()}")
                    try {
                        Log.d(TAG, "输出张量[$i] 量化参数: 缩放比例=${outputTensor.quantizationParams().scale}, 零点=${outputTensor.quantizationParams().zeroPoint}")
                    } catch (e: Exception) {
                        Log.d(TAG, "输出张量[$i] 无量化参数")
                    }
                }
                
                Log.d(TAG, "模型版本: Adobe Scan capture_6_22_23")
                Log.d(TAG, "================================")
            } catch (e: Exception) {
                Log.e(TAG, "获取模型信息失败", e)
            }
        }
    }
    
    /**
     * 释放资源
     */
    fun release() {
        if (useNativeDetection) {
            // 释放本地资源
            releaseModelNative()
        } else {
            // 释放Java资源
            captureInterpreter?.close()
            resizeInterpreter?.close()
            pageTurnInterpreter?.close()
            
            captureInterpreter = null
            resizeInterpreter = null
            pageTurnInterpreter = null
        }
        
        isInitialized = false
        Log.d(TAG, "模型资源已释放")
    }
    
    /**
     * 检查是否已初始化
     */
    fun isReady(): Boolean = isInitialized
}
