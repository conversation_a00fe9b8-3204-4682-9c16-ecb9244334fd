#!/usr/bin/env python3
"""
Adobe Scan 文档边缘检测 Python 实现
基于逆向工程提取的 TensorFlow Lite 模型

主要功能：
- 加载 Adobe Scan 的 capture_6_22_23.tflite 模型
- 检测图像中的文档边缘
- 返回4个角点的归一化坐标
- 可视化检测结果

技术规格：
- 输入：640x480 灰度图像
- 输出：4个角点坐标 (归一化到 0-1 范围)
- 模型大小：2.42 MB
- 推理时间：~50ms (取决于硬件)
"""

import cv2
import numpy as np
import tensorflow as tf
import argparse
import os
import time
from typing import Tuple, List, Optional
import matplotlib.pyplot as plt


class AdobeScanDocumentDetector:
    """Adobe Scan 文档边缘检测器"""
    
    def __init__(self, model_path: str = "FindDocEdgeDemo/app/src/main/assets/models/capture_6_22_23.tflite"):
        """
        初始化文档检测器
        
        Args:
            model_path: Adobe Scan 模型文件路径
        """
        self.model_path = model_path
        self.interpreter = None
        self.input_details = None
        self.output_details = None
        self.input_shape = (320, 320)  # 根据实际模型规格
        
        self._load_model()
    
    def _load_model(self):
        """加载 TensorFlow Lite 模型"""
        try:
            if not os.path.exists(self.model_path):
                raise FileNotFoundError(f"模型文件不存在: {self.model_path}")
            
            print(f"正在加载 Adobe Scan 模型: {self.model_path}")
            
            # 加载模型
            self.interpreter = tf.lite.Interpreter(model_path=self.model_path)
            self.interpreter.allocate_tensors()
            
            # 获取输入输出信息
            self.input_details = self.interpreter.get_input_details()
            self.output_details = self.interpreter.get_output_details()
            
            print("模型加载成功!")
            print(f"输入形状: {self.input_details[0]['shape']}")
            print(f"输出形状: {self.output_details[0]['shape']}")
            print(f"输入数据类型: {self.input_details[0]['dtype']}")
            print(f"输出数据类型: {self.output_details[0]['dtype']}")
            
        except Exception as e:
            print(f"模型加载失败: {e}")
            raise
    
    def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        图像预处理，按照 Adobe Scan 的实际规格

        Args:
            image: 输入图像 (BGR 格式)

        Returns:
            预处理后的图像数组 [1, 320, 320, 4]
        """
        # 调整大小到 320x320
        resized = cv2.resize(image, self.input_shape)

        # 确保是3通道BGR图像
        if len(resized.shape) == 2:
            resized = cv2.cvtColor(resized, cv2.COLOR_GRAY2BGR)
        elif resized.shape[2] == 4:
            resized = cv2.cvtColor(resized, cv2.COLOR_BGRA2BGR)

        # 模型期望4通道输入，添加alpha通道
        alpha_channel = np.ones((320, 320, 1), dtype=np.uint8) * 255
        rgba_image = np.concatenate([resized, alpha_channel], axis=2)

        # 添加批次维度: (320, 320, 4) -> (1, 320, 320, 4)
        processed = np.expand_dims(rgba_image, axis=0)

        return processed
    
    def detect_document_edges(self, image: np.ndarray) -> Optional[np.ndarray]:
        """
        检测文档边缘
        
        Args:
            image: 输入图像
            
        Returns:
            4个角点的归一化坐标 [(x1,y1), (x2,y2), (x3,y3), (x4,y4)]
            坐标范围 0-1，如果检测失败返回 None
        """
        if self.interpreter is None:
            raise RuntimeError("模型未加载")
        
        try:
            # 预处理图像
            processed_image = self.preprocess_image(image)
            
            # 设置输入
            self.interpreter.set_tensor(self.input_details[0]['index'], processed_image)
            
            # 运行推理
            start_time = time.time()
            self.interpreter.invoke()
            inference_time = (time.time() - start_time) * 1000
            
            # 获取输出
            output_data = self.interpreter.get_tensor(self.output_details[0]['index'])
            
            print(f"推理时间: {inference_time:.2f} ms")
            print(f"输出形状: {output_data.shape}")
            print(f"输出数据: {output_data}")
            
            # 解析输出 - 实际模型输出13个点，我们取前4个作为文档角点
            if output_data.size >= 26:  # 13个点，每个点2个坐标
                # 重塑为 (13, 2) 格式
                all_points = output_data.reshape(-1, 2)

                # 取前4个点作为文档的四个角点
                corners = all_points[:4]

                # 确保坐标在 0-1 范围内
                corners = np.clip(corners, 0.0, 1.0)

                print(f"检测到 {len(all_points)} 个点，使用前4个作为文档角点")

                return corners
            else:
                print(f"输出数据格式不符合预期，期望26个值，实际得到{output_data.size}个")
                return None
                
        except Exception as e:
            print(f"检测失败: {e}")
            return None
    
    def visualize_detection(self, image: np.ndarray, corners: np.ndarray, 
                          save_path: Optional[str] = None) -> np.ndarray:
        """
        可视化检测结果
        
        Args:
            image: 原始图像
            corners: 检测到的角点 (归一化坐标)
            save_path: 保存路径 (可选)
            
        Returns:
            绘制了检测结果的图像
        """
        result_image = image.copy()
        h, w = image.shape[:2]
        
        # 将归一化坐标转换为像素坐标
        pixel_corners = corners * np.array([w, h])
        pixel_corners = pixel_corners.astype(np.int32)
        
        # 绘制角点
        for i, (x, y) in enumerate(pixel_corners):
            cv2.circle(result_image, (x, y), 8, (0, 255, 0), -1)
            cv2.putText(result_image, f"{i+1}", (x+10, y-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        # 绘制边缘线
        if len(pixel_corners) == 4:
            # 连接角点形成文档边框
            pts = pixel_corners.reshape((-1, 1, 2))
            cv2.polylines(result_image, [pts], True, (0, 255, 255), 3)
        
        # 添加检测信息
        cv2.putText(result_image, "Adobe Scan Document Detection", 
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        cv2.putText(result_image, f"Corners: {len(corners)}", 
                   (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        # 保存结果
        if save_path:
            cv2.imwrite(save_path, result_image)
            print(f"检测结果已保存到: {save_path}")
        
        return result_image


def main():
    """主函数 - 命令行接口"""
    parser = argparse.ArgumentParser(description="Adobe Scan 文档边缘检测")
    parser.add_argument("--image", "-i", required=True, help="输入图像路径")
    parser.add_argument("--model", "-m", 
                       default="FindDocEdgeDemo/app/src/main/assets/models/capture_6_22_23.tflite",
                       help="模型文件路径")
    parser.add_argument("--output", "-o", help="输出图像路径")
    parser.add_argument("--show", "-s", action="store_true", help="显示结果")
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not os.path.exists(args.image):
        print(f"错误: 图像文件不存在 - {args.image}")
        return
    
    # 初始化检测器
    try:
        detector = AdobeScanDocumentDetector(args.model)
    except Exception as e:
        print(f"初始化失败: {e}")
        return
    
    # 加载图像
    print(f"正在处理图像: {args.image}")
    image = cv2.imread(args.image)
    if image is None:
        print("错误: 无法加载图像")
        return
    
    print(f"图像尺寸: {image.shape}")
    
    # 检测文档边缘
    corners = detector.detect_document_edges(image)
    
    if corners is not None:
        print("检测成功!")
        print("检测到的角点坐标 (归一化):")
        for i, (x, y) in enumerate(corners):
            print(f"  角点 {i+1}: ({x:.4f}, {y:.4f})")
        
        # 可视化结果
        result_image = detector.visualize_detection(image, corners, args.output)
        
        # 显示结果
        if args.show:
            cv2.imshow("Adobe Scan Document Detection", result_image)
            print("按任意键关闭窗口...")
            cv2.waitKey(0)
            cv2.destroyAllWindows()
    else:
        print("检测失败: 未能检测到文档边缘")


if __name__ == "__main__":
    main()
