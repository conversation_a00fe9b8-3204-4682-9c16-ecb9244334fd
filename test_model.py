#!/usr/bin/env python3
"""
测试 Adobe Scan 模型加载和基本功能
"""

import os
import sys
import numpy as np
import cv2

def check_dependencies():
    """检查依赖是否安装"""
    print("检查依赖...")
    
    try:
        import tensorflow as tf
        print(f"✅ TensorFlow: {tf.__version__}")
    except ImportError:
        print("❌ TensorFlow 未安装")
        return False
    
    try:
        import cv2
        print(f"✅ OpenCV: {cv2.__version__}")
    except ImportError:
        print("❌ OpenCV 未安装")
        return False
    
    try:
        import numpy as np
        print(f"✅ NumPy: {np.__version__}")
    except ImportError:
        print("❌ NumPy 未安装")
        return False
    
    return True

def check_model_files():
    """检查模型文件是否存在"""
    print("\n检查模型文件...")
    
    model_dir = "FindDocEdgeDemo/app/src/main/assets/models"
    if not os.path.exists(model_dir):
        print(f"❌ 模型目录不存在: {model_dir}")
        return False
    
    models = [
        "capture_6_22_23.tflite",
        "page_turn_2im_002.tflite", 
        "resize_to_320.tflite"
    ]
    
    for model in models:
        model_path = os.path.join(model_dir, model)
        if os.path.exists(model_path):
            size = os.path.getsize(model_path) / (1024 * 1024)  # MB
            print(f"✅ {model}: {size:.2f} MB")
        else:
            print(f"❌ {model}: 不存在")
    
    return True

def test_model_loading():
    """测试模型加载"""
    print("\n测试模型加载...")
    
    try:
        import tensorflow as tf
        
        model_path = "FindDocEdgeDemo/app/src/main/assets/models/capture_6_22_23.tflite"
        
        if not os.path.exists(model_path):
            print(f"❌ 主模型文件不存在: {model_path}")
            return False
        
        # 加载模型
        interpreter = tf.lite.Interpreter(model_path=model_path)
        interpreter.allocate_tensors()
        
        # 获取输入输出信息
        input_details = interpreter.get_input_details()
        output_details = interpreter.get_output_details()
        
        print("✅ 模型加载成功!")
        print(f"输入详情:")
        for i, detail in enumerate(input_details):
            print(f"  输入 {i}: 形状={detail['shape']}, 类型={detail['dtype']}")
        
        print(f"输出详情:")
        for i, detail in enumerate(output_details):
            print(f"  输出 {i}: 形状={detail['shape']}, 类型={detail['dtype']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return False

def test_basic_inference():
    """测试基本推理"""
    print("\n测试基本推理...")
    
    try:
        import tensorflow as tf
        
        model_path = "FindDocEdgeDemo/app/src/main/assets/models/capture_6_22_23.tflite"
        interpreter = tf.lite.Interpreter(model_path=model_path)
        interpreter.allocate_tensors()
        
        input_details = interpreter.get_input_details()
        output_details = interpreter.get_output_details()
        
        # 创建测试输入
        input_shape = input_details[0]['shape']
        print(f"期望输入形状: {input_shape}")
        
        # 创建随机测试数据
        if input_details[0]['dtype'] == np.float32:
            test_input = np.random.random(input_shape).astype(np.float32)
        else:
            test_input = np.random.randint(0, 255, input_shape).astype(input_details[0]['dtype'])
        
        # 运行推理
        interpreter.set_tensor(input_details[0]['index'], test_input)
        interpreter.invoke()
        
        # 获取输出
        output_data = interpreter.get_tensor(output_details[0]['index'])
        
        print("✅ 推理测试成功!")
        print(f"输出形状: {output_data.shape}")
        print(f"输出数据类型: {output_data.dtype}")
        print(f"输出范围: [{output_data.min():.4f}, {output_data.max():.4f}]")
        
        return True
        
    except Exception as e:
        print(f"❌ 推理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 Adobe Scan 模型测试")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请安装必要的依赖:")
        print("pip install tensorflow opencv-python numpy")
        return
    
    # 检查模型文件
    if not check_model_files():
        print("\n❌ 模型文件检查失败")
        return
    
    # 测试模型加载
    if not test_model_loading():
        print("\n❌ 模型加载测试失败")
        return
    
    # 测试基本推理
    if not test_basic_inference():
        print("\n❌ 推理测试失败")
        return
    
    print("\n" + "=" * 50)
    print("🎉 所有测试通过!")
    print("现在可以运行 document_edge_detector.py 进行文档边缘检测")

if __name__ == "__main__":
    main()
