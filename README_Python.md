# Adobe Scan 文档边缘检测 Python 实现

基于逆向工程提取的 Adobe Scan AI 模型的 Python 实现，用于检测图像中的文档边缘。

## 🚀 功能特性

- ✅ 使用 Adobe Scan 原生 TensorFlow Lite 模型 (`capture_6_22_23.tflite`)
- ✅ 检测图像中的文档四个角点
- ✅ 输出归一化坐标 (0-1 范围)
- ✅ 可视化检测结果
- ✅ 支持批量处理
- ✅ 命令行接口
- ✅ Python API

## 📋 技术规格

| 项目 | 规格 |
|------|------|
| 模型文件 | `capture_6_22_23.tflite` (2.42 MB) |
| 输入尺寸 | 640×480 灰度图像 |
| 输出格式 | 4个角点的归一化坐标 |
| 推理时间 | ~50ms (取决于硬件) |
| 支持格式 | JPG, PNG, BMP 等常见图像格式 |

## 🛠️ 安装依赖

```bash
# 安装基本依赖
pip install -r requirements.txt

# 或者手动安装
pip install tensorflow>=2.8.0 opencv-python numpy matplotlib
```

## 🧪 快速测试

首先运行测试脚本确保环境配置正确：

```bash
python test_model.py
```

预期输出：
```
🧪 Adobe Scan 模型测试
==================================================
检查依赖...
✅ TensorFlow: 2.x.x
✅ OpenCV: 4.x.x
✅ NumPy: 1.x.x

检查模型文件...
✅ capture_6_22_23.tflite: 2.42 MB
✅ page_turn_2im_002.tflite: X.XX MB
✅ resize_to_320.tflite: X.XX MB

测试模型加载...
✅ 模型加载成功!
输入详情:
  输入 0: 形状=[1, 480, 640, 1], 类型=<class 'numpy.float32'>
输出详情:
  输出 0: 形状=[1, 8], 类型=<class 'numpy.float32'>

测试基本推理...
✅ 推理测试成功!
==================================================
🎉 所有测试通过!
```

## 📖 使用方法

### 1. 命令行使用

```bash
# 基本使用
python document_edge_detector.py --image your_document.jpg

# 保存结果
python document_edge_detector.py --image your_document.jpg --output result.jpg

# 显示结果窗口
python document_edge_detector.py --image your_document.jpg --show

# 使用自定义模型
python document_edge_detector.py --image your_document.jpg --model path/to/model.tflite
```

### 2. Python API 使用

```python
from document_edge_detector import AdobeScanDocumentDetector
import cv2

# 初始化检测器
detector = AdobeScanDocumentDetector()

# 加载图像
image = cv2.imread("your_document.jpg")

# 检测文档边缘
corners = detector.detect_document_edges(image)

if corners is not None:
    print("检测成功!")
    print("角点坐标 (归一化):")
    for i, (x, y) in enumerate(corners):
        print(f"  角点 {i+1}: ({x:.4f}, {y:.4f})")
    
    # 可视化结果
    result_image = detector.visualize_detection(image, corners, "result.jpg")
else:
    print("检测失败")
```

### 3. 运行演示

```bash
python example_usage.py
```

这将运行完整的演示，包括：
- 创建测试文档图像
- 运行边缘检测
- 批量处理示例
- 真实图像处理

## 📁 文件结构

```
.
├── document_edge_detector.py    # 主要检测器类
├── example_usage.py            # 使用示例和演示
├── test_model.py              # 模型测试脚本
├── requirements.txt           # Python 依赖
├── README_Python.md          # 本文档
└── FindDocEdgeDemo/
    └── app/src/main/assets/models/
        ├── capture_6_22_23.tflite      # 主要边缘检测模型
        ├── page_turn_2im_002.tflite    # 页面翻转检测模型
        └── resize_to_320.tflite        # 图像缩放模型
```

## 🔧 API 参考

### AdobeScanDocumentDetector 类

#### 初始化
```python
detector = AdobeScanDocumentDetector(model_path="path/to/model.tflite")
```

#### 主要方法

**`detect_document_edges(image)`**
- 输入: OpenCV 图像 (BGR 格式)
- 输出: numpy 数组 shape=(4, 2)，包含4个角点的归一化坐标
- 返回: 成功时返回坐标数组，失败时返回 None

**`visualize_detection(image, corners, save_path=None)`**
- 输入: 原始图像、角点坐标、保存路径(可选)
- 输出: 绘制了检测结果的图像
- 功能: 在图像上绘制角点和边框

**`preprocess_image(image)`**
- 输入: 原始图像
- 输出: 预处理后的图像数组
- 功能: 按照模型要求进行图像预处理

## 📊 性能指标

| 指标 | 值 |
|------|---|
| 模型大小 | 2.42 MB |
| 推理时间 (CPU) | ~50ms |
| 推理时间 (GPU) | ~10ms |
| 内存占用 | ~100MB |
| 准确率 | 取决于图像质量 |

## 🐛 故障排除

### 常见问题

1. **模型文件不存在**
   ```
   错误: 模型文件不存在: FindDocEdgeDemo/app/src/main/assets/models/capture_6_22_23.tflite
   ```
   解决: 确保模型文件路径正确，或使用 `--model` 参数指定正确路径

2. **TensorFlow 版本不兼容**
   ```
   错误: 模型加载失败
   ```
   解决: 升级 TensorFlow 到 2.8.0 或更高版本

3. **图像加载失败**
   ```
   错误: 无法加载图像
   ```
   解决: 检查图像文件路径和格式

4. **检测失败**
   ```
   检测失败: 未能检测到文档边缘
   ```
   解决: 
   - 确保图像中有清晰的文档边缘
   - 尝试调整图像亮度和对比度
   - 确保文档占据图像的主要部分

## 🔬 技术细节

### 模型输入输出规格

**输入:**
- 形状: `[1, 480, 640, 1]`
- 类型: `float32`
- 范围: `[0.0, 1.0]` (归一化)
- 格式: 灰度图像

**输出:**
- 形状: `[1, 8]` 或 `[1, 4, 2]`
- 类型: `float32`
- 范围: `[0.0, 1.0]` (归一化坐标)
- 格式: 4个角点的 (x, y) 坐标

### 坐标系统

- 坐标原点: 图像左上角 (0, 0)
- X 轴: 向右为正
- Y 轴: 向下为正
- 坐标范围: [0.0, 1.0] (归一化)

## 📄 许可证

本项目仅用于研究和学习目的。Adobe Scan 模型的版权归 Adobe Inc. 所有。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 📞 联系

如有问题或建议，请创建 GitHub Issue。
